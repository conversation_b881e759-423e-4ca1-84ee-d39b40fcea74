<template>
  <div class="cloud-medical-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏云医</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/hanshiyunyi.png" 
          alt="汉氏云医" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏云医</h2>
          <p class="banner-subtitle">打破时空界限，您的全生命周期线上健康管家</p>
        </div>
      </div>

      <!-- 核心功能模块 -->
      <div class="section features-section">
        <h2 class="section-title"><el-icon><Monitor /></el-icon> 核心功能 · 您的掌上健康中心</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="feature-content">
              <h3>在线问诊</h3>
              <p>足不出户，与海内外名医集团的专家实时图文、语音、视频沟通。</p>
            </div>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="feature-content">
              <h3>健康管理</h3>
              <p>体检报告深度解读、慢病指标追踪、基因报告咨询、私人健康计划定制。</p>
            </div>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="feature-content">
              <h3>电子档案</h3>
              <p>建立您和家人的终身私密电子健康档案，随时随地查阅。</p>
            </div>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="feature-content">
              <h3>绿色通道</h3>
              <p>线上线下无缝衔接，为您快速预约线下专家门诊、检查及住院服务。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务流程模块 -->
      <div class="section process-section">
        <h2 class="section-title"><el-icon><Guide /></el-icon> 服务流程 · 轻松三步</h2>
        <div class="process-flow">
          <div class="process-step">
            <div class="step-header">
              <div class="step-number">1</div>
              <h3>注册并选择</h3>
            </div>
            <div class="step-content">
              <p>在小程序中轻松注册，根据您的需求选择相应的专家或健康服务。</p>
            </div>
            <div class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="process-step">
            <div class="step-header">
              <div class="step-number">2</div>
              <h3>线上沟通</h3>
            </div>
            <div class="step-content">
              <p>通过安全加密的通道，与您的医生或健康管理师进行深度沟通。</p>
            </div>
            <div class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="process-step">
            <div class="step-header">
              <div class="step-number">3</div>
              <h3>获取方案</h3>
            </div>
            <div class="step-content">
              <p>接收个性化的诊疗建议、健康报告解读或持续的管理方案。</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Monitor,
  ChatDotRound,
  DataAnalysis,
  Folder,
  Connection,
  Guide,
  ArrowRight
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.cloud-medical-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px;
  padding-bottom: 80px;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.5s ease;
}

.banner-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease;
}

.banner-section:hover .banner-image {
  transform: scale(1.03);
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%);
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #0f9da8;
  margin: 0 0 15px 0;
  gap: 8px;
}

/* 核心功能网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.feature-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #edf2f7;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-icon .el-icon {
  font-size: 32px;
  color: white;
}

.feature-content h3 {
  font-size: 18px;
  margin: 0 0 12px 0;
  color: #333;
  font-weight: 600;
}

.feature-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 服务流程样式 */
.process-flow {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.process-step {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #edf2f7;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.process-step:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 20px;
  box-shadow: 0 5px 15px rgba(15, 157, 168, 0.3);
  flex-shrink: 0;
}

.step-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.step-content p {
  font-size: 15px;
  line-height: 1.6;
  color: #666;
  margin: 0;
}

.step-arrow {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #0f9da8;
  font-size: 24px;
  background: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s infinite;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .process-flow {
    flex-direction: row;
    align-items: stretch;
  }

  .process-step {
    flex: 1;
    margin-right: 30px;
  }

  .process-step:last-child {
    margin-right: 0;
  }

  .process-step:last-child .step-arrow {
    display: none;
  }

  .banner-title {
    font-size: 32px;
  }

  .banner-subtitle {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .step-arrow {
    display: none;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .feature-card {
    flex-direction: row;
    text-align: left;
    align-items: flex-start;
    gap: 15px;
    padding: 16px;
  }

  .feature-icon {
    flex-shrink: 0;
    margin-bottom: 0;
    width: 50px;
    height: 50px;
  }

  .feature-icon .el-icon {
    font-size: 28px;
  }

  .feature-content {
    flex: 1;
  }

  .feature-content h3 {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .feature-content p {
    font-size: 13px;
  }

  .process-step {
    padding: 16px;
  }

  .step-number {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .step-header h3 {
    font-size: 18px;
  }

  .step-content p {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .feature-card {
    padding: 24px;
    flex-direction: column;
    text-align: center;
    align-items: center;
  }

  .feature-icon {
    margin-bottom: 15px;
  }

  .feature-content h3 {
    font-size: 20px;
  }

  .feature-content p {
    font-size: 15px;
  }
}
</style>
