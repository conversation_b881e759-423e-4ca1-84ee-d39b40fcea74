<template>
  <div class="third-hospital-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏联合三级医院(筹)</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/sanjiyiyuan.png"
          alt="汉氏联合三级医院"
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏联合三级医院(筹)</h2>
          <p class="banner-subtitle">擘画未来，一座以细胞治疗为核心的智慧型医院</p>
        </div>
      </div>

      <!-- 蓝图数据化模块 -->
      <div class="section blueprint-section">
        <h2 class="section-title"><el-icon><DataAnalysis /></el-icon> 蓝图数据化 · 未来可期</h2>
        <div class="blueprint-data">
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">107</span>
              <span class="data-unit">亩</span>
            </div>
            <div class="data-label">规划占地面积</div>
          </div>
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">500</span>
              <span class="data-unit">张</span>
            </div>
            <div class="data-label">设计床位数量</div>
          </div>
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">8.3万</span>
              <span class="data-unit">㎡</span>
            </div>
            <div class="data-label">总建筑面积</div>
          </div>
          <div class="data-item">
            <div class="data-number">
              <span class="data-main">2024</span>
              <span class="data-unit">年</span>
            </div>
            <div class="data-label">预计投入运营</div>
          </div>
        </div>
      </div>

      <!-- 核心学科规划模块 -->
      <div class="section discipline-section">
        <h2 class="section-title"><el-icon><Medal /></el-icon> 核心学科规划 · 精准定位</h2>
        <p class="discipline-subtitle">打造特色鲜明的区域医疗新高地</p>

        <!-- 中心核心 -->
        <div class="discipline-center-card">
          <div class="center-badge">核心</div>
          <div class="center-icon">🧬</div>
          <h3 class="center-title">细胞治疗中心</h3>
          <p class="center-desc">以细胞治疗为核心，整合多学科资源，构建区域医疗新高地</p>
          <div class="center-features">
            <span class="feature-tag">干细胞治疗</span>
            <span class="feature-tag">免疫细胞治疗</span>
            <span class="feature-tag">再生医学</span>
          </div>
        </div>

        <!-- 辐射科室 -->
        <div class="discipline-cards">
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><FirstAidKit /></el-icon>
            </div>
            <h4 class="card-title">血液病科</h4>
            <p class="card-desc">专业血液疾病诊疗</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <h4 class="card-title">肿瘤科</h4>
            <p class="card-desc">肿瘤综合治疗中心</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <h4 class="card-title">心脑血管科</h4>
            <p class="card-desc">心脑血管疾病防治</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><User /></el-icon>
            </div>
            <h4 class="card-title">老年病科</h4>
            <p class="card-desc">老年综合医疗服务</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><Cpu /></el-icon>
            </div>
            <h4 class="card-title">风湿免疫科</h4>
            <p class="card-desc">免疫系统疾病诊疗</p>
          </div>
          <div class="discipline-card">
            <div class="card-icon">
              <el-icon><Female /></el-icon>
            </div>
            <h4 class="card-title">高端妇产科</h4>
            <p class="card-desc">高端妇产医疗服务</p>
          </div>
        </div>
      </div>

      <!-- 产医融合模块 -->
      <div class="section integration-section">
        <h2 class="section-title"><el-icon><Connection /></el-icon> 产医融合 · 独特优势</h2>
        <p class="integration-subtitle">从实验室到病床，转化一步到位</p>

        <!-- 融合优势展示 -->
        <div class="integration-advantages">
          <div class="advantage-card">
            <div class="advantage-number">1</div>
            <div class="advantage-content">
              <div class="advantage-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              <h3 class="advantage-title">细胞谷</h3>
              <p class="advantage-desc">国家级细胞库与研发中心</p>
              <ul class="advantage-features">
                <li>国际标准细胞库</li>
                <li>GMP级别生产车间</li>
                <li>前沿研发实验室</li>
              </ul>
            </div>
          </div>

          <div class="advantage-card">
            <div class="advantage-number">2</div>
            <div class="advantage-content">
              <div class="advantage-icon">
                <el-icon><Link /></el-icon>
              </div>
              <h3 class="advantage-title">无缝对接</h3>
              <p class="advantage-desc">院士工作站与博士后科研工作站</p>
              <ul class="advantage-features">
                <li>院士专家团队</li>
                <li>博士后研究平台</li>
                <li>产学研一体化</li>
              </ul>
            </div>
          </div>

          <div class="advantage-card">
            <div class="advantage-number">3</div>
            <div class="advantage-content">
              <div class="advantage-icon">
                <el-icon><FirstAidKit /></el-icon>
              </div>
              <h3 class="advantage-title">三级医院</h3>
              <p class="advantage-desc">临床研究与应用转化中心</p>
              <ul class="advantage-features">
                <li>临床试验基地</li>
                <li>转化医学平台</li>
                <li>精准医疗服务</li>
              </ul>
            </div>
          </div>
        </div>



      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
// 导入所需的组件和图标
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  DataAnalysis,
  Medal,
  FirstAidKit,
  Monitor,
  User,
  Cpu,
  Female,
  Connection,
  DataBoard,
  Link
} from '@element-plus/icons-vue'

import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.third-hospital-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px; /* 减少内边距 */
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px; /* 减小圆角 */
  overflow: hidden;
  margin-bottom: 20px; /* 减少下边距 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transition: all 0.5s ease; /* 添加过渡效果 */
}

.banner-section:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease; /* 添加图片过渡效果 */
}

.banner-section:hover .banner-image {
  transform: scale(1.03); /* 悬停时图片轻微放大 */
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px; /* 减少内边距 */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%); /* 增强渐变效果 */
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px; /* 减小圆角 */
  padding: 20px; /* 内边距 */
  margin-bottom: 20px; /* 下边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 20px; /* 字体大小 */
  color: #0f9da8;
  margin: 0 0 20px 0; /* 下边距 */
  gap: 8px;
}

/* 蓝图数据化模块样式 */
.blueprint-data {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.data-item {
  background: linear-gradient(135deg, #f0f9fa, #e6f7f8);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #d0eef0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.data-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(15, 157, 168, 0.15);
}

.data-number {
  font-size: 36px;
  font-weight: 700;
  color: #0f9da8;
  line-height: 1.2;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 3px;
  margin-bottom: 10px;
}

.data-main {
  font-size: 36px;
}

.data-unit {
  font-size: 16px;
  color: #0f9da8;
  font-weight: 500;
  margin-left: 2px;
}

.data-label {
  font-size: 14px;
  color: #666;
}

/* 核心学科规划模块样式 */
.discipline-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  text-align: center;
}

/* 中心核心卡片 */
.discipline-center-card {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  border-radius: 20px;
  padding: 30px;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(15, 157, 168, 0.3);
  transition: all 0.4s ease;
  margin-bottom: 30px;
}

.discipline-center-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(15, 157, 168, 0.4);
}

.center-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.center-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.9;
}

.center-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 15px 0;
}

.center-desc {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.9;
}

.center-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* 专科科室卡片网格 */
.discipline-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.discipline-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  border: 2px solid #f0f9fa;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.discipline-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.discipline-card:hover::before {
  transform: scaleX(1);
}

.discipline-card:hover {
  transform: translateY(-5px);
  border-color: #0f9da8;
  box-shadow: 0 10px 30px rgba(15, 157, 168, 0.15);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f9fa, #e6f7f8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f9da8;
  font-size: 20px;
  margin: 0 auto 12px;
  transition: all 0.3s ease;
}

.discipline-card:hover .card-icon {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  transform: scale(1.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.card-desc {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* 产医融合模块样式 */
.integration-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  text-align: center;
}

/* 融合优势展示 */
.integration-advantages {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  margin-bottom: 30px;
}

.advantage-card {
  background: white;
  border-radius: 20px;
  padding: 25px;
  border: 2px solid #f0f9fa;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.advantage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.advantage-card:hover::before {
  transform: scaleX(1);
}

.advantage-card:hover {
  transform: translateY(-8px);
  border-color: #0f9da8;
  box-shadow: 0 15px 40px rgba(15, 157, 168, 0.15);
}

.advantage-number {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 700;
  box-shadow: 0 5px 15px rgba(15, 157, 168, 0.3);
}

.advantage-content {
  padding-right: 50px;
}

.advantage-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f9fa, #e6f7f8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f9da8;
  font-size: 24px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.advantage-card:hover .advantage-icon {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  transform: scale(1.1);
}

.advantage-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.advantage-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.advantage-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.advantage-features li {
  font-size: 13px;
  color: #0f9da8;
  margin-bottom: 6px;
  position: relative;
  padding-left: 15px;
}

.advantage-features li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #0f9da8;
  font-weight: bold;
}

/* 融合流程图 */
.integration-flow {
  background: linear-gradient(135deg, #f8fafc, #f0f9fa);
  border-radius: 20px;
  padding: 30px;
  border: 2px solid #e6f7f8;
}

.flow-title {
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  color: #0f9da8;
  margin-bottom: 30px;
}

.flow-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flow-step {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 15px;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.flow-step:hover .step-number {
  transform: scale(1.1);
}

.step-content {
  max-width: 120px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.step-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.flow-arrow {
  font-size: 24px;
  color: #0f9da8;
  margin: 0 15px;
  opacity: 0.7;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .blueprint-data {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }

  .data-item {
    padding: 25px;
  }

  .discipline-cards {
    grid-template-columns: repeat(3, 1fr);
  }

  .integration-advantages {
    grid-template-columns: repeat(3, 1fr);
  }

  .flow-steps {
    flex-direction: row;
  }
}

@media (max-width: 1024px) and (min-width: 768px) {
  .integration-advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 767px) {
  .section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 18px;
    margin-bottom: 15px;
  }

  .integration-subtitle {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .blueprint-data {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .data-item {
    padding: 18px 15px;
  }

  .data-number {
    font-size: 30px;
  }

  .data-unit {
    font-size: 15px;
  }

  .data-label {
    font-size: 13px;
  }

  .discipline-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .integration-advantages {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 20px;
  }

  .flow-steps {
    flex-direction: column;
    gap: 20px;
  }

  .flow-arrow {
    transform: rotate(90deg);
    margin: 10px 0;
  }

  .step-content {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .blueprint-data {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .data-item {
    padding: 15px 12px;
  }

  .data-number {
    font-size: 26px;
  }

  .data-unit {
    font-size: 13px;
  }

  .data-label {
    font-size: 12px;
  }

  .discipline-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .discipline-center-card {
    padding: 25px 20px;
  }

  .center-title {
    font-size: 20px;
  }

  .center-desc {
    font-size: 14px;
  }

  .advantage-card {
    padding: 15px;
    border-radius: 12px;
  }

  .advantage-content {
    padding-right: 0;
  }

  .advantage-number {
    position: static;
    margin: 0 auto 12px auto;
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .advantage-icon {
    width: 45px;
    height: 45px;
    font-size: 18px;
    margin-bottom: 12px;
  }

  .advantage-title {
    font-size: 15px;
    margin-bottom: 8px;
  }

  .advantage-desc {
    font-size: 12px;
    margin-bottom: 10px;
    line-height: 1.4;
  }

  .advantage-features {
    margin: 0;
    padding: 0;
  }

  .advantage-features li {
    font-size: 11px;
    margin-bottom: 4px;
    padding-left: 12px;
  }

  .integration-flow {
    padding: 20px;
  }

  .flow-title {
    font-size: 18px;
  }
}

@media (max-width: 360px) {
  .discipline-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}
</style>