<template>
  <div class="medical-tourism-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏医疗旅游中心</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/yiliaolvyou.png" 
          alt="汉氏医疗旅游中心" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏医疗旅游中心</h2>
          <p class="banner-subtitle">在自然中疗愈，在科技中探索生命之美</p>
        </div>
      </div>

      <!-- "生命之旅"三部曲模块 -->
      <div class="section journey-section">
        <h2 class="section-title"><el-icon><Guide /></el-icon> "生命之旅"三部曲</h2>
        <div class="journey-flow">
          <div class="journey-step">
            <div class="step-header">
              <div class="step-number">1</div>
              <h3>探索生命</h3>
            </div>
            <div class="step-content">
              <div class="content-item">
                <strong>内容：</strong>科普研学、细胞科技馆参观、趣味实验
              </div>
              <div class="harvest-item">
                <strong>收获：</strong>知识与好奇心
              </div>
            </div>
            <div class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="journey-step">
            <div class="step-header">
              <div class="step-number">2</div>
              <h3>感悟生命</h3>
            </div>
            <div class="step-content">
              <div class="content-item">
                <strong>内容：</strong>深度健康检测、专家咨询、定制健康方案
              </div>
              <div class="harvest-item">
                <strong>收获：</strong>健康与规划
              </div>
            </div>
            <div class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="journey-step">
            <div class="step-header">
              <div class="step-number">3</div>
              <h3>健康生命</h3>
            </div>
            <div class="step-content">
              <div class="content-item">
                <strong>内容：</strong>细胞疗愈、康养调理、自然休憩
              </div>
              <div class="harvest-item">
                <strong>收获：</strong>活力与新生
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 权威认可模块 -->
      <div class="section authority-section">
        <h2 class="section-title"><el-icon><Trophy /></el-icon> 权威认可 · 品质保证</h2>
        <div class="authority-cards">
          <div class="authority-card">
            <div class="authority-icon">
              <el-icon><Star /></el-icon>
            </div>
            <h3>国家AAA级旅游景区</h3>
            <p>景区级环境，国家级认证。</p>
          </div>
          <div class="authority-card">
            <div class="authority-icon">
              <el-icon><Location /></el-icon>
            </div>
            <h3>江西省康养旅游精品线路</h3>
            <p>官方推荐，品质之选。</p>
          </div>
          <div class="authority-card">
            <div class="authority-icon">
              <el-icon><Compass /></el-icon>
            </div>
            <h3>国家中医药健康旅游示范区</h3>
            <p>融合传统智慧与现代科技。</p>
          </div>
        </div>
      </div>

      <!-- 适合人群模块 -->
      <div class="section target-section">
        <h2 class="section-title"><el-icon><UserFilled /></el-icon> 适合人群</h2>
        <div class="target-list">
          <div class="target-item">
            <div class="target-icon">
              <el-icon><House /></el-icon>
            </div>
            <div class="target-content">
              <h3>追求深度健康体验的家庭</h3>
              <p>全家共享健康之旅，在自然环境中获得身心疗愈</p>
            </div>
          </div>
          <div class="target-item">
            <div class="target-icon">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="target-content">
              <h3>寻求创新团建活动的企业</h3>
              <p>独特的团队建设体验，提升员工凝聚力和健康意识</p>
            </div>
          </div>
          <div class="target-item">
            <div class="target-icon">
              <el-icon><Medal /></el-icon>
            </div>
            <div class="target-content">
              <h3>需要身心疗愈的高端康养人士</h3>
              <p>专业的康养服务，享受尊贵的健康管理体验</p>
            </div>
          </div>
          <div class="target-item">
            <div class="target-icon">
              <el-icon><School /></el-icon>
            </div>
            <div class="target-content">
              <h3>对生命科学充满好奇的青少年</h3>
              <p>寓教于乐的科普体验，激发对生命科学的兴趣</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Guide,
  ArrowRight,
  Trophy,
  Star,
  Location,
  Compass,
  UserFilled,
  House,
  OfficeBuilding,
  Medal,
  School
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.medical-tourism-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px;
  padding-bottom: 80px;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.5s ease;
}

.banner-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease;
}

.banner-section:hover .banner-image {
  transform: scale(1.03);
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%);
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #0f9da8;
  margin: 0 0 15px 0;
  gap: 8px;
}

/* 生命之旅流程样式 */
.journey-flow {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.journey-step {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #edf2f7;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.journey-step:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 20px;
  box-shadow: 0 5px 15px rgba(15, 157, 168, 0.3);
  flex-shrink: 0;
}

.step-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.content-item, .harvest-item {
  font-size: 15px;
  line-height: 1.6;
  color: #666;
}

.content-item strong, .harvest-item strong {
  color: #333;
  font-weight: 600;
}

.harvest-item {
  color: #0f9da8;
  font-weight: 500;
}

.step-arrow {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #0f9da8;
  font-size: 24px;
  background: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s infinite;
}

/* 权威认可卡片 */
.authority-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.authority-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.authority-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.authority-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.authority-card:hover .authority-icon {
  transform: scale(1.1);
}

.authority-icon .el-icon {
  font-size: 32px;
  color: white;
}

.authority-card h3 {
  font-size: 18px;
  margin: 0 0 12px 0;
  color: #333;
  font-weight: 600;
}

.authority-card p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 适合人群列表 */
.target-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.target-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #edf2f7;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.target-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.target-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.target-item:hover .target-icon {
  transform: scale(1.1);
}

.target-icon .el-icon {
  font-size: 24px;
  color: white;
}

.target-content {
  flex: 1;
}

.target-content h3 {
  font-size: 18px;
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.target-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .journey-flow {
    flex-direction: row;
    align-items: stretch;
  }

  .journey-step {
    flex: 1;
    margin-right: 30px;
  }

  .journey-step:last-child {
    margin-right: 0;
  }

  .journey-step:last-child .step-arrow {
    display: none;
  }

  .authority-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .target-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .banner-title {
    font-size: 32px;
  }

  .banner-subtitle {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .step-arrow {
    display: none;
  }

  .target-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 16px;
  }

  .target-icon {
    width: 45px;
    height: 45px;
    margin-bottom: 12px;
  }

  .target-icon .el-icon {
    font-size: 22px;
  }

  .target-content {
    width: 100%;
    text-align: center;
  }

  .target-content h3 {
    font-size: 16px;
    margin-bottom: 8px;
    text-align: center;
    width: 100%;
  }

  .target-content p {
    font-size: 13px;
    text-align: center;
    width: 100%;
  }

  .journey-step {
    padding: 16px;
  }

  .step-number {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .step-header h3 {
    font-size: 18px;
  }

  .content-item, .harvest-item {
    font-size: 14px;
  }

  .authority-card {
    padding: 16px;
  }

  .authority-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }

  .authority-icon .el-icon {
    font-size: 28px;
  }

  .authority-card h3 {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .authority-card p {
    font-size: 13px;
  }
}
</style>
