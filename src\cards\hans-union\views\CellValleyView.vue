<template>
  <div class="cell-valley-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>国际细胞谷</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/xibaogu.png" 
          alt="国际细胞谷" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">国际细胞谷</h2>
          <p class="banner-subtitle">构筑产业生态，打造世界级细胞科技创新中心</p>
        </div>
      </div>

      <!-- 园区功能规划模块 -->
      <div class="section planning-section">
        <h2 class="section-title"><el-icon><MapLocation /></el-icon> 园区功能规划 · 一站式生态</h2>
        <div class="planning-grid">
          <div class="planning-card research">
            <div class="card-icon">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="card-content">
              <h3>研发核心区</h3>
              <p>再生医学研究院、院士工作站</p>
            </div>
          </div>
          
          <div class="planning-card medical">
            <div class="card-icon">
              <el-icon><FirstAidKit /></el-icon>
            </div>
            <div class="card-content">
              <h3>医疗服务区</h3>
              <p>三级医院、体检中心、康养中心</p>
            </div>
          </div>
          
          <div class="planning-card industry">
            <div class="card-icon">
              <el-icon><Tools /></el-icon>
            </div>
            <div class="card-content">
              <h3>产业孵化区</h3>
              <p>高标准厂房、共享实验室</p>
            </div>
          </div>
          
          <div class="planning-card business">
            <div class="card-icon">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="card-content">
              <h3>商务生活区</h3>
              <p>会议中心、专家公寓、商务酒店</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心引力模块 -->
      <div class="section attraction-section">
        <h2 class="section-title"><el-icon><Magnet /></el-icon> 为何选择入驻？ · 四大核心引力</h2>
        <div class="attraction-cards">
          <div class="attraction-card">
            <div class="attraction-icon">
              <el-icon><Document /></el-icon>
            </div>
            <h3>政策支持</h3>
            <p>享受国家、省、市级多重生物医药产业扶持政策。</p>
          </div>
          
          <div class="attraction-card">
            <div class="attraction-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <h3>技术协同</h3>
            <p>共享汉氏联合的研发平台、细胞资源库和临床转化渠道。</p>
          </div>
          
          <div class="attraction-card">
            <div class="attraction-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <h3>人才聚集</h3>
            <p>依托院士工作站，汇聚国内外顶尖科学家与产业人才。</p>
          </div>
          
          <div class="attraction-card">
            <div class="attraction-icon">
              <el-icon><Money /></el-icon>
            </div>
            <h3>资本对接</h3>
            <p>链接国内外知名投资机构，为入驻企业提供融资支持。</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  MapLocation,
  Cpu,
  FirstAidKit,
  Tools,
  OfficeBuilding,
  Magnet,
  Document,
  Connection,
  UserFilled,
  Money
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.cell-valley-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px;
  padding-bottom: 80px;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.5s ease;
}

.banner-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease;
}

.banner-section:hover .banner-image {
  transform: scale(1.03);
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%);
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #0f9da8;
  margin: 0 0 15px 0;
  gap: 8px;
}

/* 园区规划网格 */
.planning-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.planning-card {
  display: flex;
  align-items: center;
  gap: 15px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.planning-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.planning-card.research {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.planning-card.medical {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
}

.planning-card.industry {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.planning-card.business {
  border-color: #8b5cf6;
  background: linear-gradient(135deg, #f5f3ff, #ede9fe);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.planning-card.research .card-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.planning-card.medical .card-icon {
  background: linear-gradient(135deg, #10b981, #047857);
}

.planning-card.industry .card-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.planning-card.business .card-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.planning-card:hover .card-icon {
  transform: scale(1.1);
}

.card-icon .el-icon {
  font-size: 32px;
  color: white;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 20px;
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.card-content p {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 核心引力卡片 */
.attraction-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.attraction-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.attraction-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.attraction-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.attraction-card:hover .attraction-icon {
  transform: scale(1.1);
}

.attraction-icon .el-icon {
  font-size: 32px;
  color: white;
}

.attraction-card h3 {
  font-size: 18px;
  margin: 0 0 12px 0;
  color: #333;
  font-weight: 600;
}

.attraction-card p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .planning-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .attraction-cards {
    gap: 20px;
  }

  .banner-title {
    font-size: 32px;
  }

  .banner-subtitle {
    font-size: 20px;
  }
}

@media (min-width: 1024px) {
  .planning-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .planning-card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .card-content h3 {
    font-size: 18px;
  }

  .card-content p {
    font-size: 14px;
  }

  /* 桌面端保持两行两列布局 */
  .attraction-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .attraction-card {
    padding: 24px;
  }

  .attraction-card h3 {
    font-size: 20px;
  }

  .attraction-card p {
    font-size: 15px;
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .planning-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
    gap: 12px;
  }

  .card-icon {
    width: 50px;
    height: 50px;
  }

  .card-icon .el-icon {
    font-size: 28px;
  }

  .card-content h3 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .card-content p {
    font-size: 14px;
  }

  .attraction-card {
    padding: 16px;
  }

  .attraction-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }

  .attraction-icon .el-icon {
    font-size: 28px;
  }

  .attraction-card h3 {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .attraction-card p {
    font-size: 13px;
  }
}
</style>
