<template>
  <div class="medical-beauty-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏医美抗衰中心</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/yimei.png" 
          alt="汉氏医美抗衰中心" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏医美抗衰中心</h2>
          <p class="banner-subtitle">源自细胞科技，实现科技抗衰的本质回归</p>
        </div>
      </div>

      <!-- 我们的理念模块 -->
      <div class="section concept-section">
        <h2 class="section-title"><el-icon><Star /></el-icon> 我们的理念 · 对比之见</h2>
        <div class="comparison-container">
          <div class="comparison-card traditional">
            <div class="card-header">
              <h3>传统医美</h3>
            </div>
            <div class="card-content">
              <div class="comparison-item">
                <span class="label">方式：</span>
                <span class="value">填充、提拉、表面修饰</span>
              </div>
              <div class="comparison-item">
                <span class="label">效果：</span>
                <span class="value">即时性，暂时性改变外观</span>
              </div>
            </div>
          </div>
          
          <div class="vs-divider">
            <div class="vs-circle">VS</div>
          </div>
          
          <div class="comparison-card modern">
            <div class="card-header">
              <h3>汉氏医美</h3>
            </div>
            <div class="card-content">
              <div class="comparison-item">
                <span class="label">方式：</span>
                <span class="value">细胞赋活、再生修复</span>
              </div>
              <div class="comparison-item">
                <span class="label">效果：</span>
                <span class="value">由内而外，延缓衰老进程</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全保障模块 -->
      <div class="section safety-section">
        <h2 class="section-title"><el-icon><Shield /></el-icon> 安全保障 · 美丽无忧</h2>
        <div class="safety-cards">
          <div class="safety-card">
            <div class="safety-icon">
              <el-icon><Medal /></el-icon>
            </div>
            <h3>正品联盟</h3>
            <p>所有产品、设备均与国际顶尖厂商合作，源头可溯。</p>
          </div>
          <div class="safety-card">
            <div class="safety-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <h3>无菌操作</h3>
            <p>遵循严格的医疗无菌标准，杜绝一切感染风险。</p>
          </div>
          <div class="safety-card">
            <div class="safety-icon">
              <el-icon><User /></el-icon>
            </div>
            <h3>专家亲诊</h3>
            <p>所有操作均由经验丰富的执业医师亲自主理。</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Star, 

  Medal,
  CircleCheck,
  User
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.medical-beauty-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px;
  padding-bottom: 80px;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.5s ease;
}

.banner-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease;
}

.banner-section:hover .banner-image {
  transform: scale(1.03);
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%);
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #0f9da8;
  margin: 0 0 15px 0;
  gap: 8px;
}

/* 对比卡片样式 */
.comparison-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.comparison-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  max-width: 400px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.comparison-card.traditional {
  border-color: #e2e8f0;
}

.comparison-card.modern {
  border-color: #0f9da8;
  background: linear-gradient(135deg, #f0fdff, #e6fffa);
}

.comparison-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 15px 0;
  text-align: center;
  color: #333;
}

.comparison-card.modern .card-header h3 {
  color: #0f9da8;
}

.comparison-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.comparison-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #666;
  min-width: 50px;
  flex-shrink: 0;
}

.value {
  color: #333;
  line-height: 1.5;
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.vs-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 15px rgba(15, 157, 168, 0.3);
}

/* 安全保障卡片 */
.safety-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.safety-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.safety-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.safety-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.safety-card:hover .safety-icon {
  transform: scale(1.1);
}

.safety-icon .el-icon {
  font-size: 32px;
  color: white;
}

.safety-card h3 {
  font-size: 18px;
  margin: 0 0 12px 0;
  color: #333;
  font-weight: 600;
}

.safety-card p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .comparison-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
  }
  
  .comparison-card {
    flex: 1;
    max-width: none;
  }
  
  .vs-divider {
    margin: 0 20px;
    align-self: center;
  }
  
  .safety-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  .banner-title {
    font-size: 32px;
  }
  
  .banner-subtitle {
    font-size: 20px;
  }
}
</style>
