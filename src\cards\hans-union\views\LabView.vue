<template>
  <div class="lab-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏医学检验实验室</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/jianyanshiyanshi.png" 
          alt="汉氏医学检验实验室" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏医学检验实验室</h2>
          <p class="banner-subtitle">以生命科学之精准，解码您的健康密语</p>
        </div>
      </div>

      <!-- 我们的承诺模块 -->
      <div class="section core-concept-section">
        <h2 class="section-title"><el-icon><Star /></el-icon> 我们的承诺</h2>
        <div class="concept-cards">
          <div class="concept-card">
            <div class="concept-icon">
              <el-icon><Cpu /></el-icon>
            </div>
            <h3>技术前沿</h3>
            <p>聚焦细胞、基因、分子层面的检测，探索生命最本源的奥秘。</p>
          </div>
          <div class="concept-card">
            <div class="concept-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <h3>结果公正</h3>
            <p>与金域医学全面合作，遵循国际质控标准，确保每一份数据的公正与权威。</p>
          </div>
          <div class="concept-card">
            <div class="concept-icon">
              <el-icon><Lock /></el-icon>
            </div>
            <h3>数据安全</h3>
            <p>严格遵守隐私保护协议，您的健康信息将被最高级别加密守护。</p>
          </div>
        </div>
      </div>

      <!-- 核心检测平台模块 -->
      <div class="section platform-section">
        <h2 class="section-title"><el-icon><Connection /></el-icon> 核心检测平台 · 实力矩阵</h2>
        <p class="platform-subtitle">六大平台，构筑精准检测矩阵</p>
        
        <!-- 六边形布局 -->
        <div class="platform-matrix">
          <div class="platform-item">
            <div class="platform-icon">
              <el-icon><Cellphone /></el-icon>
            </div>
            <h3>血细胞分析平台</h3>
          </div>
          
          <div class="platform-item">
            <div class="platform-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <h3>临床生化平台</h3>
          </div>
          
          <div class="platform-item">
            <div class="platform-icon">
              <el-icon><Histogram /></el-icon>
            </div>
            <h3>临床免疫平台</h3>
          </div>
          
          <div class="platform-item">
            <div class="platform-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <h3>流式细胞分析平台</h3>
          </div>
          
          <div class="platform-item">
            <div class="platform-icon">
              <el-icon><Opportunity /></el-icon>
            </div>
            <h3>分子生物学平台</h3>
          </div>
          
          <div class="platform-item">
            <div class="platform-icon">
              <el-icon><FirstAidKit /></el-icon>
            </div>
            <h3>临床病理平台</h3>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Star, 
  Cpu, 
  Lock,
  Connection,
  Cellphone,
  Monitor,
  Histogram,
  DataAnalysis,
  Opportunity,
  FirstAidKit,
  CircleCheck
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.lab-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px; /* 减少内边距 */
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px; /* 减小圆角 */
  overflow: hidden;
  margin-bottom: 20px; /* 减少下边距 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transition: all 0.5s ease; /* 添加过渡效果 */
}

.banner-section:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease; /* 添加图片过渡效果 */
}

.banner-section:hover .banner-image {
  transform: scale(1.03); /* 悬停时图片轻微放大 */
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px; /* 减少内边距 */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%); /* 增强渐变效果 */
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  margin-bottom: 16px; /* 减少下边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px; /* 减小字体大小 */
  color: #0f9da8;
  margin: 0 0 15px 0; /* 减少下边距 */
  gap: 8px;
}

/* 我们的承诺卡片 */
.concept-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px; /* 减少间距 */
}

.concept-card {
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

.concept-card:hover {
  transform: translateY(-3px); /* 增强悬停效果 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.concept-icon {
  width: 50px; /* 减小图标尺寸 */
  height: 50px; /* 减小图标尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px; /* 减少下边距 */
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.concept-card:hover .concept-icon {
  transform: scale(1.1); /* 悬停时图标放大 */
}

.concept-icon .el-icon {
  font-size: 28px; /* 增大图标 */
  color: white;
}

.concept-card h3 {
  font-size: 20px; /* 增大标题 */
  margin: 0 0 14px 0; /* 增加下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
}

.concept-card p {
  font-size: 15px; /* 增大描述文字 */
  color: #666;
  line-height: 1.7; /* 增加行高 */
  margin: 0;
}

/* 核心检测平台 */
.platform-section {
  text-align: center;
}

.platform-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.platform-matrix {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.platform-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.platform-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.platform-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.platform-item:hover .platform-icon {
  transform: scale(1.1);
}

.platform-icon .el-icon {
  font-size: 30px;
  color: white;
}

.platform-item h3 {
  font-size: 16px;
  margin: 0;
  color: #333;
  font-weight: 600;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .concept-cards {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .platform-matrix {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .banner-title {
    font-size: 32px; /* 增大标题 */
  }
  
  .banner-subtitle {
    font-size: 20px; /* 增大副标题 */
  }
}

@media (min-width: 992px) {
  .platform-matrix {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .concept-card {
    padding: 16px;
  }

  .concept-icon {
    width: 45px;
    height: 45px;
    margin-bottom: 12px;
  }

  .concept-icon .el-icon {
    font-size: 24px;
  }

  .concept-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .concept-card p {
    font-size: 14px;
  }

  .platform-item {
    padding: 12px;
  }

  .platform-item h4 {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .platform-item p {
    font-size: 12px;
  }
}
</style>