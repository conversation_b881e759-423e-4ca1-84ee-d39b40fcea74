<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 产品技术参数
const technicalSpecs = reactive([
  { 
    model: 'FXF-Φ150', 
    inletSize: '240×420', 
    outletSize: '380×310', 
    dustOutletSize: '30',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '380×310',
    weight: '30'
  },
  { 
    model: 'FXF-Φ200', 
    inletSize: '320×540', 
    outletSize: '480×380', 
    dustOutletSize: '50',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '480×380',
    weight: '50'
  },
  { 
    model: 'FXF-Φ250', 
    inletSize: '400×680', 
    outletSize: '568×2049', 
    dustOutletSize: '75',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '568×2049',
    weight: '75'
  },
  { 
    model: 'FXF-Φ300', 
    inletSize: '520×1480', 
    outletSize: '680×2450', 
    dustOutletSize: '100',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '680×2450',
    weight: '100'
  },
  { 
    model: 'FXF-Φ350', 
    inletSize: '1250×2710', 
    outletSize: '718×2860', 
    dustOutletSize: '140',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '718×2860',
    weight: '140'
  },
  { 
    model: 'FXF-Φ400', 
    inletSize: '1250×3000', 
    outletSize: '808×3277', 
    dustOutletSize: '225',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '808×3277',
    weight: '225'
  },
  { 
    model: 'FXF-Φ450', 
    inletSize: '2100×3800', 
    outletSize: '893×3605', 
    dustOutletSize: '280',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '893×3605',
    weight: '280'
  },
  { 
    model: 'FXF-Φ500', 
    inletSize: '2500×4600', 
    outletSize: '983×4100', 
    dustOutletSize: '350',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '983×4100',
    weight: '350'
  },
  { 
    model: 'FXF-Φ600', 
    inletSize: '3700×6750', 
    outletSize: '1159×4934', 
    dustOutletSize: '610',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '1159×4934',
    weight: '610'
  },
  { 
    model: 'FXF-Φ700', 
    inletSize: '5000×8200', 
    outletSize: '1335×5751', 
    dustOutletSize: '800',
    efficiency: '70-95',
    resistance: '500-2000',
    dimensions: '1335×5751',
    weight: '800'
  }
])

// 性能特点
const productFeatures = reactive([
  {
    title: '上升的含尘气流在旋风内部进行旋转运动',
    description: '含尘气流从切向进入旋风除尘器，在内部形成强烈的旋转运动，利用离心力实现粉尘分离。'
  },
  {
    title: '分离出来的尘粒在重力的作用下',
    description: '经过离心分离的粉尘颗粒在重力作用下沿器壁下降，最终收集在底部的集尘斗中。'
  },
  {
    title: '净化后的气体由中心管向上排出',
    description: '经过旋风分离净化的洁净气体通过中心排气管向上排出，完成整个除尘过程。'
  },
  {
    title: '适用于捕集干燥的非纤维性粉尘',
    description: '特别适用于处理干燥、非纤维性的粉尘颗粒，对于粗颗粒粉尘具有良好的分离效果。'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '建材行业',
    description: '广泛应用于水泥、石灰、石膏等建材生产过程中的粉尘收集和处理。'
  },
  {
    title: '冶金行业',
    description: '用于钢铁冶炼、有色金属加工等过程中产生的金属粉尘和颗粒物的分离。'
  },
  {
    title: '化工行业',
    description: '适用于化工生产过程中各种干燥粉状物料的收集和回收处理。'
  },
  {
    title: '电力行业',
    description: '用于燃煤电厂的飞灰收集，作为电除尘器或布袋除尘器的预处理设备。'
  },
  {
    title: '粮食加工',
    description: '适用于粮食加工、饲料生产等行业的粉尘收集和物料回收。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>旋风除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/a00d6b51-5145-4275-9c7d-b53e975fabb8.jpg" alt="旋风除尘器" />
        </div>
        <div class="hero-content">
          <h2>FXF系列旋风除尘器</h2>
          <p class="product-subtitle">利用离心力分离粉尘的高效除尘设备</p>
          <div class="product-intro">
            <p>旋风除尘器是一种利用含尘气流作旋转运动，借助于离心力降尘粒从气流中分离并捕集于器壁，再借助重力作用使尘粒落入灰斗的除尘装置。</p>
            <p>旋风除尘器的各个部件都没有运动部件，制造、安装和维护管理都很方便，并且设备投资和操作费用都较低，已广泛用于从气流中分离固体和液体粒子，或从液体中分离固体粒子。</p>
            <p>在普通操作条件下，作用于粒子上的离心力是重力的5～2500倍，所以旋风除尘器的效率显著高于重力沉降室。</p>
          </div>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>进口尺寸<br/>(mm)</th>
                <th>出口尺寸<br/>(mm)</th>
                <th>排灰口<br/>(mm)</th>
                <th>除尘效率<br/>(%)</th>
                <th>阻力损失<br/>(Pa)</th>
                <th>外形尺寸<br/>(mm)</th>
                <th>重量<br/>(kg)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.inletSize }}</td>
                <td>{{ spec.outletSize }}</td>
                <td>{{ spec.dustOutletSize }}</td>
                <td>{{ spec.efficiency }}</td>
                <td>{{ spec.resistance }}</td>
                <td>{{ spec.dimensions }}</td>
                <td>{{ spec.weight }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🌪️</div>
            <div class="indicator-content">
              <h4>除尘效率</h4>
              <div class="indicator-value">70-95%</div>
              <p>高效粉尘分离</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚡</div>
            <div class="indicator-content">
              <h4>阻力损失</h4>
              <div class="indicator-value">500-2000Pa</div>
              <p>低阻力运行</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔧</div>
            <div class="indicator-content">
              <h4>结构特点</h4>
              <div class="indicator-value">无运动部件</div>
              <p>维护简便</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">💰</div>
            <div class="indicator-content">
              <h4>经济性</h4>
              <div class="indicator-value">投资低</div>
              <p>运行费用少</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🌡️</div>
            <div class="indicator-content">
              <h4>适用条件</h4>
              <div class="indicator-value">高温高压</div>
              <p>恶劣环境适应</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔍</div>
            <div class="indicator-content">
              <h4>适用粒径</h4>
              <div class="indicator-value">粗颗粒</div>
              <p>干燥非纤维性</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🏗️</div>
            <div class="advantage-content">
              <h4>结构简单可靠</h4>
              <p>设备结构简单，无运动部件，制造、安装和维护管理都很方便，设备可靠性高，故障率低。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">💰</div>
            <div class="advantage-content">
              <h4>投资运行成本低</h4>
              <p>设备投资和操作费用都较低，制造成本低，维护费用少，是经济实用的除尘设备。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🌡️</div>
            <div class="advantage-content">
              <h4>适应恶劣环境</h4>
              <p>能够在高温、高压、腐蚀性气体等恶劣环境下正常工作，适应性强，使用范围广。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">⚡</div>
            <div class="advantage-content">
              <h4>处理能力大</h4>
              <p>单位体积处理气量大，占地面积小，可以处理大风量的含尘气体，适用于大型工业生产。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔄</div>
            <div class="advantage-content">
              <h4>物料回收价值</h4>
              <p>分离出的粉尘可以回收利用，特别适用于有价值物料的回收，实现资源的循环利用。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🎯</div>
            <div class="advantage-content">
              <h4>离心分离效率高</h4>
              <p>利用离心力进行分离，离心力是重力的5～2500倍，分离效率显著高于重力沉降室。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <div class="principle-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>切向进气</h4>
              <p>含尘气流从旋风除尘器的切向进气口高速进入，在圆筒内形成向下的外旋流。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>离心分离</h4>
              <p>气流在旋转过程中产生强大的离心力，密度大的粉尘颗粒被甩向器壁。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>粉尘下降</h4>
              <p>被分离的粉尘颗粒沿器壁在重力和气流作用下向下运动，进入集尘斗。</p>
            </div>
          </div>
          <div class="principle-step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>洁净气体排出</h4>
              <p>净化后的气体在锥体底部转向形成向上的内旋流，通过排气管排出。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 选型指南 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📋</span>
          选型指南
        </h3>
        <div class="selection-guide">
          <div class="guide-item">
            <h4>🌪️ 处理风量</h4>
            <p>根据实际需要处理的气体流量选择合适的型号规格，确保设备能够满足生产需求。</p>
          </div>
          <div class="guide-item">
            <h4>🔍 粉尘特性</h4>
            <p>考虑粉尘的粒径分布、密度、湿度等特性，旋风除尘器适用于处理5μm以上的粗颗粒粉尘。</p>
          </div>
          <div class="guide-item">
            <h4>🌡️ 工况条件</h4>
            <p>考虑气体温度、压力、腐蚀性等工况条件，选择合适的材质和结构形式。</p>
          </div>
          <div class="guide-item">
            <h4>📊 除尘效率要求</h4>
            <p>根据环保要求和生产需要确定除尘效率指标，必要时可采用多级旋风除尘器。</p>
          </div>
          <div class="guide-item">
            <h4>💰 经济性考虑</h4>
            <p>综合考虑设备投资、运行费用、维护成本等因素，选择性价比最优的方案。</p>
          </div>
          <div class="guide-item">
            <h4>🔧 安装空间</h4>
            <p>考虑现场安装空间限制，选择合适的设备尺寸和布置方式。</p>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.3;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.55rem;
  min-width: 1000px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.4rem 0.2rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.5rem;
  line-height: 1.1;
  border: 1px solid #2563eb;
}

.specs-table td {
  padding: 0.4rem 0.2rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.55rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

/* 应用场景 */
.applications {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.application-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 产品优势 */
.advantages {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 工作原理 */
.working-principle {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.principle-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
  border-left: 4px solid #3b82f6;
}

.principle-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.step-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.step-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.step-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 选型指南 */
.selection-guide {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.guide-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.guide-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.guide-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.guide-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
  }

  .feature-card p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .specs-table {
    font-size: 0.6rem;
  }

  .specs-table th {
    padding: 0.6rem 0.3rem;
    font-size: 0.55rem;
  }

  .specs-table td {
    padding: 0.6rem 0.3rem;
    font-size: 0.6rem;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .application-card {
    padding: 1.5rem;
  }

  .application-card h4 {
    font-size: 1.1rem;
  }

  .application-card p {
    font-size: 0.9rem;
  }

  .working-principle {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .principle-step {
    padding: 1.5rem;
  }

  .step-content h4 {
    font-size: 1.1rem;
  }

  .step-content p {
    font-size: 0.9rem;
  }

  .selection-guide {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .guide-item {
    padding: 1.5rem;
  }

  .guide-item h4 {
    font-size: 1.1rem;
  }

  .guide-item p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.65rem;
  }

  .specs-table th {
    font-size: 0.6rem;
  }

  .specs-table td {
    font-size: 0.65rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .selection-guide {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
