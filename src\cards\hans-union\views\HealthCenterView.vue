<template>
  <div class="health-center-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>汉氏医学健康体检中心</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/jiankangtijianzhongxin.png" 
          alt="汉氏医学健康体检中心" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">汉氏医学健康体检中心</h2>
          <p class="banner-subtitle">引领精准预防医学，预见您的健康未来</p>
        </div>
      </div>

      <!-- 核心理念模块 -->
      <div class="section core-concept-section">
        <h2 class="section-title"><el-icon><Star /></el-icon> 核心理念</h2>
        <div class="concept-cards">
          <div class="concept-card">
            <div class="concept-icon">
              <el-icon><PictureFilled /></el-icon>
            </div>
            <h3>环境之美</h3>
            <p>告别传统医院的冰冷，我们在花园式的细胞谷中为您打造宁静、舒适的五星级体检空间。</p>
          </div>
          <div class="concept-card">
            <div class="concept-icon">
              <el-icon><Aim /></el-icon>
            </div>
            <h3>精准之核</h3>
            <p>不止于发现疾病，我们更专注于通过前沿科技评估您的健康风险，实现真正的"上医治未病"。</p>
          </div>
          <div class="concept-card">
            <div class="concept-icon">
              <el-icon><Service /></el-icon>
            </div>
            <h3>服务之暖</h3>
            <p>从预约到报告解读，我们提供"一对一"全程导检与健康管理师服务，让您感受有温度的关怀。</p>
          </div>
        </div>
      </div>

      <!-- 尖端科技模块 -->
      <div class="section tech-section">
        <h2 class="section-title"><el-icon><Cpu /></el-icon> 尖端科技 · 精准之基</h2>
        <div class="tech-cards">
          <div class="tech-card">
            <div class="tech-info">
              <h3>美国GE 1.5T光纤磁共振</h3>
              <p>业界唯一定量，可实现早期脑梗的精准筛查。</p>
            </div>
          </div>
          <div class="tech-card">
            <div class="tech-info">
              <h3>美国GE Revolution 16 CT</h3>
              <p>拥有8大低剂量金标准保障，是早期肺癌筛查的利器。</p>
            </div>
          </div>
          <div class="tech-card">
            <div class="tech-info">
              <h3>美国GE Senographe Crystal乳腺机</h3>
              <p>更高敏感性，更低放射剂量，提供乳腺癌筛查的最佳解决方案。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 尊享服务模块 -->
      <div class="section service-section">
        <h2 class="section-title"><el-icon><Service /></el-icon> 尊享服务 · 舒心之旅</h2>
        <!-- 卡片式服务流程 -->
        <div class="service-flow">
          <!-- 步骤1 -->
          <div class="flow-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>深度咨询预约</h3>
              <p>专业健康顾问为您定制个性化体检方案</p>
            </div>
            <div class="step-arrow"><el-icon><ArrowDown /></el-icon></div>
          </div>
          
          <!-- 步骤2 -->
          <div class="flow-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>VIP接待 & 专属更衣</h3>
              <p>尊享五星级接待与私密更衣空间</p>
            </div>
            <div class="step-arrow"><el-icon><ArrowDown /></el-icon></div>
          </div>
          
          <!-- 步骤3 -->
          <div class="flow-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>医护一对一全程导检</h3>
              <p>专业医护人员全程陪同，无需排队等待</p>
            </div>
            <div class="step-arrow"><el-icon><ArrowDown /></el-icon></div>
          </div>
          
          <!-- 步骤4 -->
          <div class="flow-step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3>营养早餐 & 休憩</h3>
              <p>精心准备的健康餐点与舒适休息环境</p>
            </div>
            <div class="step-arrow"><el-icon><ArrowDown /></el-icon></div>
          </div>
          
          <!-- 步骤5 -->
          <div class="flow-step">
            <div class="step-number">5</div>
            <div class="step-content">
              <h3>专家团三级会诊</h3>
              <p>多学科专家联合分析，确保诊断精准</p>
            </div>
            <div class="step-arrow"><el-icon><ArrowDown /></el-icon></div>
          </div>
          
          <!-- 步骤6 -->
          <div class="flow-step">
            <div class="step-number">6</div>
            <div class="step-content">
              <h3>纸质+电子档案建立</h3>
              <p>双重健康档案，便于随时查阅</p>
            </div>
            <div class="step-arrow"><el-icon><ArrowDown /></el-icon></div>
          </div>
          
          <!-- 步骤7 -->
          <div class="flow-step">
            <div class="step-number">7</div>
            <div class="step-content">
              <h3>健康管理师持续追踪</h3>
              <p>定期回访与健康指导，全方位守护您的健康</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Star, 
  Cpu, 
  Service,
  PictureFilled,
  Aim,
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.health-center-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px; /* 减少内边距 */
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px; /* 减小圆角 */
  overflow: hidden;
  margin-bottom: 20px; /* 减少下边距 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transition: all 0.5s ease; /* 添加过渡效果 */
}

.banner-section:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease; /* 添加图片过渡效果 */
}

.banner-section:hover .banner-image {
  transform: scale(1.03); /* 悬停时图片轻微放大 */
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px; /* 减少内边距 */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%); /* 增强渐变效果 */
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  margin-bottom: 16px; /* 减少下边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px; /* 减小字体大小 */
  color: #0f9da8;
  margin: 0 0 15px 0; /* 减少下边距 */
  gap: 8px;
}

/* 核心理念卡片 */
.concept-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px; /* 减少间距 */
}

.concept-card {
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

.concept-card:hover {
  transform: translateY(-3px); /* 增强悬停效果 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.concept-icon {
  width: 50px; /* 减小图标尺寸 */
  height: 50px; /* 减小图标尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px; /* 减少下边距 */
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.concept-card:hover .concept-icon {
  transform: scale(1.1); /* 悬停时图标放大 */
}

.concept-icon .el-icon {
  font-size: 28px; /* 增大图标 */
  color: white;
}

.concept-card h3 {
  font-size: 20px; /* 增大标题 */
  margin: 0 0 14px 0; /* 增加下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
}

.concept-card p {
  font-size: 15px; /* 增大描述文字 */
  color: #666;
  line-height: 1.7; /* 增加行高 */
  margin: 0;
}

/* 尖端科技卡片 */
.tech-cards {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 减少卡片间距 */
}

.tech-card {
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  overflow: hidden;
  border: 1px solid #edf2f7;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
  text-align: center; /* 居中文本 */
}

.tech-card:hover {
  transform: translateY(-3px); /* 增强悬停效果 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

/* 移除tech-image相关样式 */

.tech-info {
  padding: 20px; /* 减少内边距 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中内容 */
  align-items: center; /* 水平居中内容 */
}

.tech-info h3 {
  font-size: 20px; /* 增加标题字体大小 */
  margin: 0 0 15px 0; /* 增加下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
  position: relative; /* 为下划线定位 */
  padding-bottom: 12px; /* 为下划线留出空间 */
}

.tech-info h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  border-radius: 3px;
}

.tech-info p {
  font-size: 16px; /* 增加描述文字大小 */
  color: #666;
  line-height: 1.7; /* 增加行高 */
  margin: 0;
}

/* 服务流程 */
.service-flow {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 移动端优先：纵向排列 */
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px 14px; /* 减少内边距 */
  border: 1px solid #edf2f7;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.flow-step:hover {
  transform: translateY(-3px); /* 添加悬停效果 */
  border-color: #0f9da8;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 40px; /* 减小尺寸 */
  height: 40px; /* 减小尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 10px; /* 减少下边距 */
  flex-shrink: 0;
  font-size: 18px; /* 减小字体大小 */
  box-shadow: 0 5px 15px rgba(15, 157, 168, 0.3); /* 添加阴影 */
}

.step-content {
  flex: 1;
  width: 100%;
}

.step-content h3 {
  font-size: 16px; /* 减小标题字体大小 */
  margin: 0 0 8px 0; /* 减少下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
}

.step-content p {
  font-size: 14px; /* 减小描述文字大小 */
  color: #666;
  line-height: 1.4; /* 减小行高 */
  margin: 0;
}

.step-arrow {
  color: #0f9da8;
  font-size: 24px; /* 增加箭头大小 */
  position: absolute;
  bottom: -12px; /* 调整位置到底部 */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%); /* 水平居中偏移 */
  z-index: 1;
  animation: pulse 1.5s infinite; /* 添加脉冲动画 */
}

/* 添加脉冲动画 */
@keyframes pulse {
  0% {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
}

/* 联系卡片 */
.contact-section {
  margin-bottom: 20px; /* 减少下边距 */
}

.contact-card {
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  border-radius: 16px; /* 减小圆角 */
  padding: 20px; /* 减少内边距 */
  color: white;
  text-align: center;
  box-shadow: 0 8px 30px rgba(15, 157, 168, 0.3); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
  position: relative;
  overflow: hidden;
}

.contact-card:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(15, 157, 168, 0.4);
}

/* 添加背景动画效果 */
.contact-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
  transform: rotate(30deg);
  transition: all 0.8s ease;
  z-index: 1;
  opacity: 0;
}

.contact-card:hover::before {
  opacity: 1;
  transform: rotate(0deg);
}

.contact-card h3 {
  font-size: 20px; /* 减小标题 */
  margin: 0 0 10px 0; /* 减少下边距 */
  font-weight: 600; /* 加粗标题 */
  position: relative;
  z-index: 2;
}

.contact-card p {
  font-size: 15px; /* 减小描述文字 */
  opacity: 0.9;
  margin: 0 0 16px 0; /* 减少下边距 */
  position: relative;
  z-index: 2;
}

.contact-actions {
  display: flex;
  justify-content: center;
  gap: 15px; /* 减少间距 */
  position: relative;
  z-index: 2;
}

.contact-btn {
  background: white;
  color: #0f9da8;
  border: none;
  padding: 10px 20px; /* 减少内边距 */
  border-radius: 25px; /* 减小圆角 */
  font-weight: 600; /* 加粗文字 */
  font-size: 15px; /* 减小字体 */
  display: flex;
  align-items: center;
  gap: 8px; /* 减少间距 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.contact-btn:hover {
  background: #f8fafc;
  color: #0f9da8;
  transform: translateY(-2px); /* 添加悬停效果 */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); /* 增强阴影 */
}

/* 响应式设计 */
@media (min-width: 768px) {
  .concept-cards {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .tech-cards {
    display: grid; /* 改为网格布局 */
    grid-template-columns: repeat(3, 1fr); /* 三列布局 */
    gap: 20px; /* 减少间距 */
  }
  
  .tech-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px 16px; /* 减少内边距 */
  }
  
  /* 移除tech-image相关样式 */
  
  .tech-info {
    padding: 0;
  }
  
  .tech-info h3 {
    font-size: 22px; /* 增大标题字体 */
  }
  
  .tech-info p {
    font-size: 16px; /* 增大描述文字 */
  }
  
  .banner-title {
    font-size: 32px; /* 增大标题 */
  }
  
  .banner-subtitle {
    font-size: 20px; /* 增大副标题 */
  }
  
  .service-flow {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 平板端改为2列 */
    gap: 20px;
  }
  
  .flow-step {
    padding: 20px; /* 减少内边距 */
    height: 100%; /* 确保高度一致 */
  }
  
  .flow-step:hover {
    transform: translateY(-5px); /* 增强悬停效果 */
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }
  
  .step-number {
    width: 50px; /* 减小尺寸 */
    height: 50px; /* 减小尺寸 */
    font-size: 20px; /* 减小字体 */
    margin-bottom: 15px; /* 减少下边距 */
  }
  
  .step-content h3 {
    font-size: 18px; /* 减小标题 */
    margin-bottom: 10px; /* 减少下边距 */
  }
  
  .step-content p {
    font-size: 15px; /* 减小描述文字 */
  }
  
  .step-arrow {
    bottom: -15px; /* 调整位置到底部 */
    left: 50%; /* 水平居中 */
    transform: translateX(-50%); /* 水平居中偏移 */
    font-size: 28px; /* 增大箭头 */
  }
  
  /* 最后一行步骤没有箭头 */
  .flow-step:nth-child(n+5) .step-arrow,
  .flow-step:last-child .step-arrow {
    display: none;
  }
  
  /* 桌面端优化 */
  @media (min-width: 1024px) {
    .service-flow {
      grid-template-columns: repeat(4, 1fr); /* 桌面端4列 */
    }
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .step-arrow {
    display: none;
  }

  .concept-cards {
    gap: 12px;
  }

  .concept-card {
    padding: 16px;
  }

  .concept-icon {
    width: 45px;
    height: 45px;
    margin-bottom: 12px;
  }

  .concept-icon .el-icon {
    font-size: 24px;
  }

  .concept-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .concept-card p {
    font-size: 14px;
  }

  .tech-cards {
    gap: 12px;
  }

  .tech-card {
    padding: 16px;
  }

  .tech-info h3 {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .tech-info p {
    font-size: 14px;
  }

  .flow-step {
    padding: 16px;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 16px;
    margin-bottom: 8px;
  }

  .step-content h3 {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .step-content p {
    font-size: 13px;
  }
  
  /* 第4个步骤后的步骤移到下一行 */
  .flow-step:nth-child(n+5) {
    grid-column: span 1;
  }
  
  /* 调整第二行的位置 - 向左对齐 */
  .flow-step:nth-child(5) {
    grid-column: 1 / 2; /* 从第1列开始 */
  }
  
  .flow-step:nth-child(6) {
    grid-column: 2 / 3; /* 从第2列开始 */
  }
  
  .flow-step:nth-child(7) {
    grid-column: 3 / 4; /* 从第3列开始 */
  }
}
</style>