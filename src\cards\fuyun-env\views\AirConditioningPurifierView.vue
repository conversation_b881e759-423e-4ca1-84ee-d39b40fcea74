<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 产品技术参数
const technicalSpecs = reactive([
  { 
    model: 'FYJH-05', 
    airflow: '1800', 
    filterArea: '4.5', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '1.5',
    dimensions: '1200×800×600',
    weight: '120'
  },
  { 
    model: 'FYJH-08', 
    airflow: '3000', 
    filterArea: '7.5', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '2.2',
    dimensions: '1400×900×700',
    weight: '150'
  },
  { 
    model: 'FYJH-10', 
    airflow: '3600', 
    filterArea: '9.0', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '3.0',
    dimensions: '1600×1000×800',
    weight: '180'
  },
  { 
    model: 'FYJH-15', 
    airflow: '5400', 
    filterArea: '13.5', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '4.0',
    dimensions: '1800×1200×900',
    weight: '220'
  },
  { 
    model: 'FYJH-20', 
    airflow: '7200', 
    filterArea: '18.0', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '5.5',
    dimensions: '2000×1400×1000',
    weight: '280'
  },
  { 
    model: 'FYJH-25', 
    airflow: '9000', 
    filterArea: '22.5', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '7.5',
    dimensions: '2200×1600×1100',
    weight: '350'
  },
  { 
    model: 'FYJH-30', 
    airflow: '10800', 
    filterArea: '27.0', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '9.0',
    dimensions: '2400×1800×1200',
    weight: '420'
  },
  { 
    model: 'FYJH-40', 
    airflow: '14400', 
    filterArea: '36.0', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '11.0',
    dimensions: '2800×2000×1400',
    weight: '550'
  },
  { 
    model: 'FYJH-50', 
    airflow: '18000', 
    filterArea: '45.0', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '15.0',
    dimensions: '3200×2200×1600',
    weight: '680'
  },
  { 
    model: 'FYJH-60', 
    airflow: '21600', 
    filterArea: '54.0', 
    resistance: '≤300', 
    efficiency: '≥99.9%', 
    power: '18.5',
    dimensions: '3600×2400×1800',
    weight: '820'
  }
])

// 性能特点
const productFeatures = reactive([
  {
    title: '一体化设计',
    description: '将空调功能与净化功能完美结合，电子净化单元、生物除臭单元、高效过滤单元、可根据需要灵活组合。'
  },
  {
    title: '高效空气净化',
    description: '采用多级过滤技术，包括粗效、中效、高效过滤器，净化效率高达99.9%以上，有效去除空气中的粉尘、细菌、病毒等污染物。'
  },
  {
    title: '智能控制系统',
    description: '配备先进的智能控制系统，可实现自动运行、远程监控，具有故障自诊断功能，运行稳定可靠。'
  },
  {
    title: '节能环保',
    description: '采用高效节能技术，运行成本低，符合国家节能环保要求，为用户创造舒适健康的室内环境。'
  },
  {
    title: '模块化设计',
    description: '采用模块化设计理念，便于安装、维护和升级，可根据不同需求进行灵活配置。'
  },
  {
    title: '广泛应用',
    description: '适用于医院、学校、办公楼、商场、酒店等各类建筑的空气净化需求，提供洁净舒适的室内环境。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>组合式空调机组净化器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/8adb6c3b-dd23-42cf-8215-0c7af13e394e.jpg" alt="组合式空调机组净化器" />
        </div>
        <div class="hero-content">
          <h2>组合式空调机组净化器</h2>
          <p class="product-subtitle">集成式空气净化系统，提供洁净舒适的室内环境</p>
          <div class="product-intro">
            <p>组合式空调机组净化器是集空调制冷制热功能和空气净化功能于一体的综合性设备，可为各种建筑提供洁净舒适的室内环境。</p>
            <p>该设备采用先进的多级过滤技术，包括粗效过滤器、中效过滤器、高效过滤器等，能够有效去除空气中的粉尘、细菌、病毒等污染物，净化效率高达99.9%以上。</p>
            <p>产品具有一体化设计、智能控制、节能环保、模块化等特点，广泛应用于医院、学校、办公楼、商场、酒店等各类建筑的空气净化需求。</p>
          </div>
        </div>
      </div>

      <!-- 性能特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🔧</span>
          性能特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术参数 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术参数
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>处理风量<br/>(m³/h)</th>
                <th>过滤面积<br/>(m²)</th>
                <th>阻力<br/>(Pa)</th>
                <th>净化效率</th>
                <th>功率<br/>(kW)</th>
                <th>外形尺寸<br/>(长×宽×高mm)</th>
                <th>重量<br/>(kg)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.filterArea }}</td>
                <td>{{ spec.resistance }}</td>
                <td>{{ spec.efficiency }}</td>
                <td>{{ spec.power }}</td>
                <td>{{ spec.dimensions }}</td>
                <td>{{ spec.weight }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 核心技术指标 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          核心技术指标
        </h3>
        <div class="tech-indicators">
          <div class="indicator-card">
            <div class="indicator-icon">🌪️</div>
            <div class="indicator-content">
              <h4>净化效率</h4>
              <div class="indicator-value">≥99.9%</div>
              <p>高效去除污染物</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">⚡</div>
            <div class="indicator-content">
              <h4>处理风量</h4>
              <div class="indicator-value">1800-21600</div>
              <p>m³/h大风量处理</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🔧</div>
            <div class="indicator-content">
              <h4>系统阻力</h4>
              <div class="indicator-value">≤300Pa</div>
              <p>低阻力高效率</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🏗️</div>
            <div class="indicator-content">
              <h4>一体化设计</h4>
              <div class="indicator-value">空调+净化</div>
              <p>双功能集成</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">🧠</div>
            <div class="indicator-content">
              <h4>智能控制</h4>
              <div class="indicator-value">自动运行</div>
              <p>远程监控管理</p>
            </div>
          </div>
          <div class="indicator-card">
            <div class="indicator-icon">♻️</div>
            <div class="indicator-content">
              <h4>节能环保</h4>
              <div class="indicator-value">高效节能</div>
              <p>绿色环保技术</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          产品优势
        </h3>
        <div class="advantages">
          <div class="advantage-item">
            <div class="advantage-icon">🏗️</div>
            <div class="advantage-content">
              <h4>一体化集成设计</h4>
              <p>将空调制冷制热功能与空气净化功能完美结合，电子净化单元、生物除臭单元、高效过滤单元可根据需要灵活组合，实现一机多用。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🌪️</div>
            <div class="advantage-content">
              <h4>多级高效过滤</h4>
              <p>采用粗效、中效、高效多级过滤技术，净化效率高达99.9%以上，能够有效去除空气中的粉尘、细菌、病毒等各类污染物。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🧠</div>
            <div class="advantage-content">
              <h4>智能控制系统</h4>
              <p>配备先进的智能控制系统，可实现自动运行、远程监控，具有故障自诊断功能，运行稳定可靠，操作简便。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">♻️</div>
            <div class="advantage-content">
              <h4>节能环保技术</h4>
              <p>采用高效节能技术，运行成本低，符合国家节能环保要求，为用户创造舒适健康的室内环境，降低能耗。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🔧</div>
            <div class="advantage-content">
              <h4>模块化设计</h4>
              <p>采用模块化设计理念，便于安装、维护和升级，可根据不同需求进行灵活配置，降低维护成本。</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">🏢</div>
            <div class="advantage-content">
              <h4>广泛适用性</h4>
              <p>适用于医院、学校、办公楼、商场、酒店等各类建筑的空气净化需求，提供洁净舒适的室内环境。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications">
          <div class="application-item">
            <div class="app-icon">🏥</div>
            <h4>医疗机构</h4>
            <p>医院、诊所等医疗场所空气净化</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🎓</div>
            <h4>教育机构</h4>
            <p>学校、培训机构等教育场所</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🏢</div>
            <h4>办公楼宇</h4>
            <p>写字楼、办公室等商务场所</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🛍️</div>
            <h4>商业场所</h4>
            <p>商场、超市等商业建筑</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🏨</div>
            <h4>酒店宾馆</h4>
            <p>酒店、宾馆等住宿场所</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🏭</div>
            <h4>工业厂房</h4>
            <p>洁净车间、生产厂房</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🍽️</div>
            <h4>餐饮场所</h4>
            <p>餐厅、食堂等餐饮建筑</p>
          </div>
          <div class="application-item">
            <div class="app-icon">🏠</div>
            <h4>住宅建筑</h4>
            <p>高端住宅、别墅等居住场所</p>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品主图区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-card h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.3;
  font-size: 0.7rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.6rem;
  min-width: 1000px;
}

.specs-table th {
  background: #1e3470;
  color: white;
  padding: 0.5rem 0.3rem;
  text-align: center;
  font-weight: 600;
  font-size: 0.55rem;
  line-height: 1.1;
  border: 1px solid #2563eb;
}

.specs-table td {
  padding: 0.5rem 0.3rem;
  text-align: center;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.6rem;
  line-height: 1.1;
}

.specs-table tbody tr:hover {
  background-color: #f8fafc;
}

/* 技术指标卡片 */
.tech-indicators {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.indicator-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(30, 52, 112, 0.15);
  border-color: #3b82f6;
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
}

.indicator-icon {
  font-size: 1.8rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1e3470);
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.indicator-content {
  flex: 1;
}

.indicator-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e3470;
  line-height: 1.2;
}

.indicator-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.15rem;
  line-height: 1.1;
}

.indicator-content p {
  margin: 0;
  color: #6b7280;
  font-size: 0.7rem;
  line-height: 1.2;
}

/* 产品优势 */
.advantages {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  transition: transform 0.2s ease;
}

.advantage-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.advantage-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advantage-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-content p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 应用场景 */
.applications {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-item {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-item:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.application-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-item p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .feature-card p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .specs-table {
    font-size: 0.7rem;
  }

  .specs-table th {
    padding: 0.75rem 0.5rem;
    font-size: 0.65rem;
  }

  .specs-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.7rem;
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .indicator-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .indicator-icon {
    font-size: 2rem;
    width: 3.5rem;
    height: 3.5rem;
  }

  .indicator-content h4 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .indicator-value {
    font-size: 1.4rem;
    margin-bottom: 0.2rem;
  }

  .indicator-content p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .advantages {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }

  .advantage-content h4 {
    font-size: 1.1rem;
  }

  .advantage-content p {
    font-size: 0.9rem;
  }

  .applications {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .application-item {
    padding: 1.5rem;
  }

  .application-item h4 {
    font-size: 1.1rem;
  }

  .application-item p {
    font-size: 0.9rem;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .tech-indicators {
    grid-template-columns: repeat(3, 1fr);
  }

  .applications {
    grid-template-columns: repeat(4, 1fr);
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .specs-table {
    font-size: 0.75rem;
  }

  .specs-table th {
    font-size: 0.7rem;
  }

  .specs-table td {
    font-size: 0.75rem;
  }

  .indicator-card {
    padding: 1.75rem;
    gap: 1.25rem;
  }

  .indicator-icon {
    font-size: 2.2rem;
    width: 4rem;
    height: 4rem;
  }

  .indicator-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .indicator-value {
    font-size: 1.6rem;
    margin-bottom: 0.25rem;
  }

  .indicator-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }
}
</style>
