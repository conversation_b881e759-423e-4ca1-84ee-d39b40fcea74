<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { model: 'FKZJ-9', airflow: '9000', efficiency: '≥99.6%/≥99.8%/≥99.9%/≥100%', resistance: '<250', filterCount: '9', area: '252', pulseValves: '4' },
  { model: 'FKZJ-25', airflow: '25000', efficiency: '≥99.6%/≥99.8%/≥99.9%/≥100%', resistance: '<250', filterCount: '25', area: '700', pulseValves: '6' },
  { model: 'FKZJ-70', airflow: '70000', efficiency: '≥99.6%/≥99.8%/≥99.9%/≥100%', resistance: '<350', filterCount: '70', area: '1960', pulseValves: '8' },
  { model: 'FKZJ-90', airflow: '90000', efficiency: '≥99.6%/≥99.8%/≥99.9%/≥100%', resistance: '<350', filterCount: '90', area: '2520', pulseValves: '10' },
  { model: 'FKZJ-120', airflow: '120000', efficiency: '≥99.6%/≥99.8%/≥99.9%/≥100%', resistance: '<350', filterCount: '120', area: '3360', pulseValves: '12' },
  { model: 'FKZJ-180', airflow: '180000', efficiency: '≥99.6%/≥99.8%/≥99.9%/≥100%', resistance: '<350', filterCount: '180', area: '5040', pulseValves: '15' },
  { model: 'FKZJ-240', airflow: '240000', efficiency: '≥99.6%/≥99.8%/≥99.9%/≥100%', resistance: '<350', filterCount: '240', area: '6720', pulseValves: '18' }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '抗恶劣环境能力强',
    description: '能满足酸碱环境、近海地区多雾及梅雨季节的低区使用',
    icon: '🛡️'
  },
  {
    title: '工作阻力小',
    description: '一般在250-600pa之间，能满足进口及国产空压机入口空气过滤要求',
    icon: '⚡'
  },
  {
    title: '智能控制系统',
    description: '可与中央控制室连锁控制，三种自洁类型：时序自洁、压差自洁、手动自洁',
    icon: '🧠'
  },
  {
    title: '环保节能',
    description: '脉冲自洁耗气量低',
    icon: '🌱'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '制氧厂空分设备',
    description: '制氧厂空分设备过滤进气',
    icon: '🏭'
  },
  {
    title: '燃气轮机',
    description: '燃气轮机的进气过滤',
    icon: '⚡'
  },
  {
    title: '钢铁冶炼',
    description: '钢铁冶炼高炉鼓风',
    icon: '🔥'
  },
  {
    title: '空调新风',
    description: '空调新风过滤',
    icon: '❄️'
  },
  {
    title: '化工制药食品',
    description: '化工、制药、食品等行业',
    icon: '⚗️'
  }
])

// 技术优势
const technicalAdvantages = reactive([
  {
    title: '模块化设计',
    description: '采用模块化设计，可根据需要灵活组合，满足不同规模需求。',
    icon: '🧩'
  },
  {
    title: '先进工艺',
    description: '采用先进的脉冲喷吹清灰技术，清灰效果好，能耗低。',
    icon: '⚙️'
  },
  {
    title: '材质优良',
    description: '关键部件采用优质材料，耐腐蚀、耐高温，使用寿命长。',
    icon: '💎'
  },
  {
    title: '智能监控',
    description: '配备智能监控系统，实时监测设备运行状态，故障预警。',
    icon: '📊'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>自洁式滤筒除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/77e15d3c-1ced-4ea7-84cd-fbc809ac52fa.jpg" alt="自洁式滤筒除尘器" />
        </div>
        <div class="hero-content">
          <h2>自洁式滤筒除尘器</h2>
          <p class="product-subtitle">FKZJ脉冲反吹自洁式空气过滤器</p>
          <div class="product-intro">
            <p>空气动力设备吸入含灰尘的空气之后将造成设备的非正常磨损，吸入的灰尘将在转子表面结垢使设备转动平衡精度下降，同时下游管道设备受蚀受损。</p>
            <p>福运公司生产的FKZJ脉冲反吹自洁式空气过滤器解决了上述实际问题。因此FKZJ型系列电脑自洁式空气过滤器是各种压缩机、风机空气入口净化过滤器必不可少的配套产品。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>含尘气体从除尘器下部进入，经导流板均匀分布后进入滤筒过滤区。</p>
          <p>粉尘被滤筒外表面拦截，清洁气体从滤筒内部经上箱体排出。当滤筒表面积尘达到一定程度时，脉冲控制系统启动，压缩空气通过喷吹管瞬间喷入滤筒内部，使滤筒急剧膨胀，抖落表面粉尘，实现自动清灰。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>处理风量<br/>(m³/h)</th>
                <th>过滤效率</th>
                <th>阻力<br/>(Pa)</th>
                <th>滤筒数量<br/>(个)</th>
                <th>过滤面积<br/>(m²)</th>
                <th>脉冲阀<br/>(个)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.airflow }}</td>
                <td class="efficiency-cell">{{ spec.efficiency }}</td>
                <td>{{ spec.resistance }}</td>
                <td>{{ spec.filterCount }}</td>
                <td>{{ spec.area }}</td>
                <td>{{ spec.pulseValves }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications-grid">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <div class="app-icon">{{ app.icon }}</div>
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          技术优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in technicalAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.7rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.5rem 0.25rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  font-size: 0.65rem;
}

.specs-table td {
  padding: 0.5rem 0.25rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
  font-size: 0.7rem;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

.efficiency-cell {
  font-size: 0.6rem;
}

/* 应用场景网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 技术优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .specs-table {
    font-size: 0.75rem;
  }

  .specs-table th {
    font-size: 0.7rem;
    padding: 0.75rem 0.5rem;
  }

  .specs-table td {
    font-size: 0.75rem;
    padding: 0.75rem 0.5rem;
  }

  .efficiency-cell {
    font-size: 0.65rem;
  }

  .applications-grid {
    grid-template-columns: repeat(5, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .specs-table {
    font-size: 0.8rem;
  }

  .specs-table th {
    font-size: 0.75rem;
  }

  .specs-table td {
    font-size: 0.8rem;
  }

  .efficiency-cell {
    font-size: 0.7rem;
  }
}
</style>
