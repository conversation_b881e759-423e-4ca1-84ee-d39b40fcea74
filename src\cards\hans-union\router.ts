import { RouteRecordRaw } from 'vue-router'

// 汉氏联合AI名片路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'hansUnionHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',
      title: '汉氏联合AI名片'
    }
  },
  {
    path: '/company-intro',
    name: 'hansUnionCompanyIntro',
    component: () => import('./views/CompanyIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',
      title: '汉氏联合AI名片 - 企业介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'hansUnionAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',
      title: '汉氏联合AI名片 - AI宣传员'
    }
  },
  {
    path: '/product-center',
    name: 'hansUnionProductCenter',
    component: () => import('./views/ProductCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',
      title: '汉氏联合AI名片 - 产品中心'
    }
  },
  // 产品详情页面路由
  {
    path: '/product-center/health-center',
    name: 'hansUnionHealthCenter',
    component: () => import('./views/HealthCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',
      title: '汉氏联合AI名片 - 汉氏医学健康体检中心'
    }
  },
  {    
    path: '/product-center/medical-lab',    
    name: 'hansUnionMedicalLab',    
    component: () => import('./views/LabView.vue'),    
    meta: {       
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',      
      title: '汉氏联合AI名片 - 汉氏医学检验实验室'    
    }  
  },
  // 上饶经开汉氏联合医院页面路由
  {    
    path: '/product-center/hospital',    
    name: 'hansUnionHospital',    
    component: () => import('./views/HospitalView.vue'),    
    meta: {       
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',      
      title: '汉氏联合AI名片 - 上饶经开汉氏联合医院'    
    }  
  },
  // 汉氏联合三级医院页面路由
  {    
    path: '/product-center/third-hospital',    
    name: 'hansUnionThirdHospital',    
    component: () => import('./views/ThirdHospitalView.vue'),    
    meta: {       
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/logo3.png',      
      title: '汉氏联合AI名片 - 汉氏联合三级医院(筹)'    
    }  
  }
]

export default routes