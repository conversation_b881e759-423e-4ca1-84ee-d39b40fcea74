<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TabBar from '../components/TabBar.vue'

// 响应式数据
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const goBack = () => {
  window.location.href = '/card/fuyun-env/product-center'
}

// 技术规格数据
const technicalSpecs = reactive([
  { 
    model: 'FM-L-60', 
    dimensions: '2440×1860×5500', 
    airflow: '14500-19200', 
    filterSpeed: '2-3', 
    motorPower: '18.5/22'
  },
  { 
    model: 'FM-L-100', 
    dimensions: '2790×2440×5500', 
    airflow: '18500-23100', 
    filterSpeed: '2-3', 
    motorPower: '22/30'
  },
  { 
    model: 'FM-L-140', 
    dimensions: '3720×2440×5500', 
    airflow: '32600-37800', 
    filterSpeed: '2-3', 
    motorPower: '30/37'
  },
  { 
    model: 'FM-L-200', 
    dimensions: '5580×2440×5500', 
    airflow: '45800-56100', 
    filterSpeed: '2-3', 
    motorPower: '45/55'
  },
  { 
    model: 'FM-L-280', 
    dimensions: '7440×2440×5500', 
    airflow: '69000-76580', 
    filterSpeed: '2-3', 
    motorPower: '75'
  }
])

// 产品特点
const productFeatures = reactive([
  {
    title: '专业木工粉尘处理',
    description: '专门针对木材加工行业设计，高效收集木屑、锯末和粉尘',
    icon: '🪵'
  },
  {
    title: '大容量收集仓',
    description: '大容量粉尘收集仓，减少清理频次，提高工作效率',
    icon: '📦'
  },
  {
    title: '防火安全设计',
    description: '采用防火材料和安全设计，确保木工作业安全',
    icon: '🔥'
  },
  {
    title: '低噪音运行',
    description: '优化设计降低运行噪音，创造舒适的工作环境',
    icon: '🔇'
  }
])

// 应用场景
const applications = reactive([
  {
    title: '木材加工厂',
    description: '大型木材加工生产线的粉尘收集处理',
    icon: '🏭'
  },
  {
    title: '家具制造',
    description: '家具制造车间的木工粉尘治理',
    icon: '🪑'
  },
  {
    title: '木工车间',
    description: '各类木工车间的环境净化',
    icon: '🔨'
  },
  {
    title: '装修工地',
    description: '装修现场的木工作业粉尘控制',
    icon: '🏗️'
  }
])

// 技术优势
const technicalAdvantages = reactive([
  {
    title: '高效收集',
    description: '专业设计的收集系统，确保木工粉尘高效收集',
    icon: '🌪️'
  },
  {
    title: '安全可靠',
    description: '防火防爆设计，确保木工作业环境安全',
    icon: '🛡️'
  },
  {
    title: '节能环保',
    description: '高效节能设计，符合环保要求',
    icon: '🌱'
  },
  {
    title: '维护简便',
    description: '模块化设计，维护保养简单方便',
    icon: '🔧'
  }
])

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>木工除尘器</h1>
    </div>

    <div class="content">
      <!-- 产品主图和基本信息 -->
      <div class="product-hero">
        <div class="hero-image">
          <img src="https://omo-oss-image.thefastimg.com/portal-saas/pg2024041220292366917/cms/image/ed857385-416b-453d-b9da-030cc5c97d89.jpg" alt="木工除尘器" />
        </div>
        <div class="hero-content">
          <h2>木工除尘器</h2>
          <p class="product-subtitle">专业木工粉尘收集设备，保护木工作业环境，提高工作效率</p>
          <div class="product-intro">
            <p>木工除尘器专门为木材加工行业设计，能够有效收集和处理木工作业过程中产生的木屑、锯末和粉尘。</p>
            <p>采用专业的过滤技术，确保工作环境清洁，保护操作人员健康。</p>
          </div>
        </div>
      </div>

      <!-- 工作原理 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⚙️</span>
          工作原理
        </h3>
        <div class="working-principle">
          <p>木工除尘器通过强力风机产生负压，将木工作业产生的粉尘和木屑吸入收集系统。</p>
          <p>含尘气体经过多级过滤系统，粉尘被分离收集，清洁空气排出，实现工作环境的净化。</p>
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">✨</span>
          产品特点
        </h3>
        <div class="features-grid">
          <div
            v-for="(feature, index) in productFeatures"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">{{ feature.icon }}</div>
            <h4>{{ feature.title }}</h4>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术规格表 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          技术规格表
        </h3>
        <div class="specs-table-container">
          <table class="specs-table">
            <thead>
              <tr>
                <th>型号</th>
                <th>外形尺寸<br/>(L×W×H mm)</th>
                <th>处理风量<br/>(m³/h)</th>
                <th>过滤风速<br/>(m/min)</th>
                <th>电机功率<br/>(kW)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="spec in technicalSpecs" :key="spec.model">
                <td>{{ spec.model }}</td>
                <td>{{ spec.dimensions }}</td>
                <td>{{ spec.airflow }}</td>
                <td>{{ spec.filterSpeed }}</td>
                <td>{{ spec.motorPower }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 应用场景 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">🏗️</span>
          应用场景
        </h3>
        <div class="applications-grid">
          <div
            v-for="(app, index) in applications"
            :key="index"
            class="application-card"
          >
            <div class="app-icon">{{ app.icon }}</div>
            <h4>{{ app.title }}</h4>
            <p>{{ app.description }}</p>
          </div>
        </div>
      </div>

      <!-- 技术优势 -->
      <div class="section">
        <h3 class="section-title">
          <span class="title-icon">⭐</span>
          技术优势
        </h3>
        <div class="advantages-grid">
          <div
            v-for="(advantage, index) in technicalAdvantages"
            :key="index"
            class="advantage-card"
          >
            <div class="advantage-icon">{{ advantage.icon }}</div>
            <h4>{{ advantage.title }}</h4>
            <p>{{ advantage.description }}</p>
          </div>
        </div>
      </div>

    </div>

    <TabBar />
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  overflow-x: hidden;
}

.header {
  background: linear-gradient(135deg, #1e3470, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端隐藏返回按钮 */
@media (max-width: 768px) {
  .back-btn {
    display: none;
  }
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 产品展示区域 */
.product-hero {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  padding: 1.5rem;
}

.hero-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3470;
}

.product-subtitle {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-weight: 500;
  font-size: 1rem;
}

.product-intro p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.product-intro p:last-child {
  margin-bottom: 0;
}

/* 章节样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e3470;
}

.title-icon {
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

/* 工作原理 */
.working-principle p {
  margin: 0 0 1rem 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.working-principle p:last-child {
  margin-bottom: 0;
}

/* 产品特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.feature-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.feature-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.85rem;
}

/* 技术参数表格 */
.specs-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.specs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8rem;
}

.specs-table th {
  background: #f8fafc;
  padding: 0.75rem 0.5rem;
  text-align: center;
  font-weight: 600;
  color: #1e3470;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  font-size: 0.75rem;
}

.specs-table td {
  padding: 0.75rem 0.5rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  white-space: nowrap;
  font-size: 0.8rem;
}

.specs-table tbody tr:hover {
  background: #f8fafc;
}

/* 应用场景网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.application-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.application-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.app-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.application-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.application-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 技术优势网格 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.advantage-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  text-align: center;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
}

.advantage-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.advantage-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.advantage-card h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e3470;
}

.advantage-card p {
  margin: 0;
  color: #4b5563;
  line-height: 1.4;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-hero {
    display: flex;
    align-items: center;
    min-height: 300px;
  }

  .hero-image {
    width: 40%;
    height: 300px;
  }

  .hero-content {
    flex: 1;
    padding: 2rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .product-subtitle {
    font-size: 1.1rem;
  }

  .product-intro p {
    font-size: 1rem;
  }

  .section {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .working-principle p {
    font-size: 1rem;
  }

  .specs-table {
    font-size: 0.85rem;
  }

  .specs-table th {
    font-size: 0.8rem;
    padding: 0.75rem;
  }

  .specs-table td {
    font-size: 0.85rem;
    padding: 0.75rem;
  }

  .applications-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .specs-table {
    font-size: 0.9rem;
  }

  .specs-table th {
    font-size: 0.85rem;
  }

  .specs-table td {
    font-size: 0.9rem;
  }
}
</style>
