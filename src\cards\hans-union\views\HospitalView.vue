<template>
  <div class="hospital-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>上饶经开汉氏联合医院</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 顶部横幅 -->
      <div class="banner-section">
        <img 
          src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HanShiLianHe/lianheyiyuan.png" 
          alt="上饶经开汉氏联合医院" 
          class="banner-image"
        >
        <div class="banner-overlay">
          <h2 class="banner-title">上饶经开汉氏联合医院</h2>
          <p class="banner-subtitle">细胞科技临床转化，开启再生治疗新篇章</p>
        </div>
      </div>

      <!-- 核心特色模块 -->
      <div class="section core-feature-section">
        <h2 class="section-title"><el-icon><Star /></el-icon> 核心特色</h2>
        <div class="feature-cards">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><SetUp /></el-icon>
            </div>
            <h3>技术驱动</h3>
            <p>以再生医学为核心，将前沿的细胞治疗技术应用于临床实践，为疑难病症提供全新可能。</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <h3>专家团队</h3>
            <p>汇聚行业权威专家作为学科带头人，并建立起技术精湛、经验丰富的医疗骨干队伍。</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Service /></el-icon>
            </div>
            <h3>人文关怀</h3>
            <p>我们致力于提供一个宽松、舒适的就医环境，简化流程，注重医患沟通，传递有温度的医疗。</p>
          </div>
        </div>
      </div>

      <!-- 主要诊疗范围模块 -->
      <div class="section treatment-section">
        <h2 class="section-title"><el-icon><Operation /></el-icon> 主要诊疗范围</h2>
        <p class="treatment-subtitle">常规医疗与特色技术并行</p>
        
        <!-- 诊疗项目列表 -->
        <div class="treatment-list">
          <!-- 第一行 -->
          <div class="treatment-row">
            <div class="treatment-item">
              <div class="treatment-icon">
                <el-icon><FirstAidKit /></el-icon>
              </div>
              <h3>内科</h3>
              <p>心血管、呼吸、消化等</p>
            </div>
            
            <div class="treatment-item">
              <div class="treatment-icon">
                <el-icon><Scissor /></el-icon>
              </div>
              <h3>外科</h3>
              <p>普外、骨科、神经外科等</p>
            </div>
          </div>
          
          <!-- 第二行 -->
          <div class="treatment-row">
            <div class="treatment-item">
              <div class="treatment-icon">
                <el-icon><Female /></el-icon>
              </div>
              <h3>妇产科</h3>
              <p>妇科、产科、计划生育</p>
            </div>
            
            <div class="treatment-item">
              <div class="treatment-icon">
                <el-icon><Position /></el-icon>
              </div>
              <h3>康复医学科</h3>
              <p>功能康复、理疗、康复训练</p>
            </div>
          </div>
          
          <!-- 特色诊疗（跨越整行） -->
          <div class="treatment-item special-item">
            <div class="treatment-icon">
              <el-icon><Opportunity /></el-icon>
            </div>
            <h3>特色诊疗</h3>
            <p>细胞技术临床应用</p>
            <span class="special-tag">针对特定适应症的再生医学干预</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
// 导入所需的组件和图标
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Star, 
  SetUp, 
  UserFilled,
  Service,
  Operation,
  Female,
  Position,
  // 新增图标
  FirstAidKit,
  Scissor,
  Opportunity
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* 页面容器 */
.hospital-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 56px; /* 为固定头部留出空间 */
  padding-bottom: 80px; /* 为底部导航栏留出空间 */
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  color: white;
  z-index: 100;
  box-sizing: border-box;
  padding: 0 16px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px; /* 减少内边距 */
}

/* 顶部横幅 */
.banner-section {
  position: relative;
  border-radius: 16px; /* 减小圆角 */
  overflow: hidden;
  margin-bottom: 20px; /* 减少下边距 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transition: all 0.5s ease; /* 添加过渡效果 */
}

.banner-section:hover {
  transform: translateY(-5px); /* 添加悬停效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.8s ease; /* 添加图片过渡效果 */
}

.banner-section:hover .banner-image {
  transform: scale(1.03); /* 悬停时图片轻微放大 */
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px; /* 减少内边距 */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent 90%); /* 增强渐变效果 */
  color: white;
}

.banner-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  margin-bottom: 16px; /* 减少下边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px; /* 减小字体大小 */
  color: #0f9da8;
  margin: 0 0 15px 0; /* 减少下边距 */
  gap: 8px;
}

/* 核心特色卡片 */
.feature-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px; /* 减少间距 */
}

.feature-card {
  background: #f8fafc;
  border-radius: 12px; /* 减小圆角 */
  padding: 16px; /* 减少内边距 */
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

.feature-card:hover {
  transform: translateY(-3px); /* 增强悬停效果 */
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.feature-icon {
  width: 50px; /* 减小图标尺寸 */
  height: 50px; /* 减小图标尺寸 */
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px; /* 减少下边距 */
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.feature-card:hover .feature-icon {
  transform: scale(1.1); /* 悬停时图标放大 */
}

.feature-icon .el-icon {
  font-size: 28px; /* 增大图标 */
  color: white;
}

.feature-card h3 {
  font-size: 20px; /* 增大标题 */
  margin: 0 0 14px 0; /* 增加下边距 */
  color: #333;
  font-weight: 600; /* 加粗标题 */
}

.feature-card p {
  font-size: 15px; /* 增大描述文字 */
  color: #666;
  line-height: 1.7; /* 增加行高 */
  margin: 0;
}

/* 主要诊疗范围 */
.treatment-section {
  text-align: center;
}

.treatment-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.treatment-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

/* 新增：诊疗行样式 */
.treatment-row {
  display: flex;
  gap: 16px;
  width: 100%;
}

.treatment-row .treatment-item {
  flex: 1;
  width: 50%;
}

.treatment-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid #edf2f7;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.treatment-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: #0f9da8;
}

.treatment-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0f9da8, #1fb5c4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  box-shadow: 0 8px 20px rgba(15, 157, 168, 0.3);
  transition: all 0.3s ease;
}

.treatment-item:hover .treatment-icon {
  transform: scale(1.1);
}

.treatment-icon .el-icon {
  font-size: 30px;
  color: white;
}

.treatment-item h3 {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.treatment-item p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 特色诊疗项目样式 */
.special-item {
  width: 100%;
  background: linear-gradient(to right, #f0f9fa, #e6f7f8);
  border: 1px solid #d0eef0;
}

.special-tag {
  display: inline-block;
  background: rgba(15, 157, 168, 0.1);
  color: #0f9da8;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  margin-top: 10px;
  border: 1px solid rgba(15, 157, 168, 0.2);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .feature-cards {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .treatment-list {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .special-item {
    grid-column: span 3;
  }
  
  .banner-title {
    font-size: 32px; /* 增大标题 */
  }
  
  .banner-subtitle {
    font-size: 20px; /* 增大副标题 */
  }
}

@media (min-width: 992px) {
  .treatment-list {
    grid-template-columns: repeat(5, 1fr);
  }

  .special-item {
    grid-column: span 5;
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .feature-card {
    padding: 16px;
  }

  .feature-icon {
    width: 45px;
    height: 45px;
    margin-bottom: 12px;
  }

  .feature-icon .el-icon {
    font-size: 24px;
  }

  .feature-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .feature-card p {
    font-size: 14px;
  }

  .treatment-row {
    flex-direction: column;
    gap: 12px;
  }

  .treatment-row .treatment-item {
    width: 100%;
  }

  .treatment-item {
    padding: 16px;
  }

  .treatment-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }

  .treatment-icon .el-icon {
    font-size: 26px;
  }

  .treatment-item h3 {
    font-size: 15px;
    margin-bottom: 6px;
  }

  .treatment-item p {
    font-size: 13px;
  }

  .special-tag {
    font-size: 11px;
    padding: 3px 8px;
    margin-top: 8px;
  }
}
</style>